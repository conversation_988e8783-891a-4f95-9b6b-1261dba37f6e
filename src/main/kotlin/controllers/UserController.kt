package controllers

import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.sessions.*
import kotlinx.serialization.json.Json
import models.*
import org.slf4j.LoggerFactory
import utils.CastleService
import utils.DeviceIdManager
import utils.respondTemplate

class UserController {
    private val logger = LoggerFactory.getLogger(UserController::class.java)
    private val baseUrl = "https://api.esdiacapp.com"
    
    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
            })
        }
        
        engine {
            requestTimeout = 15000
            endpoint {
                connectTimeout = 15000
                socketTimeout = 15000
            }
        }
    }

    suspend fun getUserProfile(call: ApplicationCall) {
        val session = call.sessions.get<Session>()
        if (session == null) {
            call.respondRedirect("/auth/login")
            return
        }

        try {
            // Get device ID for the API request
            val deviceId = DeviceIdManager.getOrCreateDeviceId(call)
            
            val response = client.get("$baseUrl/v3/data/me") {
                headers {
                    append(HttpHeaders.Authorization, "Bearer ${session.token}")
                    append("X-Device-Id", deviceId)
                    append("X-Device-Type", "web")
                    headers?.get("User-Agent")?.let { append("User-Agent", it) }
                }
            }

            when (response.status) {
                HttpStatusCode.OK -> {
                    try {
                        val responseText = response.bodyAsText()
                        val userProfile = Json.decodeFromString<EsdiacUserProfile>(responseText)
                        
                        call.respondTemplate("user/profile", mapOf(
                            "user" to session,
                            "profile" to userProfile
                        ))
                    } catch (e: Exception) {
                        logger.error("Failed to parse user profile response", e)
                        call.respondTemplate("user/profile", mapOf(
                            "user" to session,
                            "error" to "Failed to load profile data"
                        ))
                    }
                }
                HttpStatusCode.Unauthorized -> {
                    // Token expired, redirect to login
                    call.sessions.clear<Session>()
                    call.respondRedirect("/auth/login")
                }
                else -> {
                    logger.error("Failed to get user profile: ${response.status}")
                    call.respondTemplate("user/profile", mapOf(
                        "user" to session,
                        "error" to "Failed to load profile data"
                    ))
                }
            }
        } catch (e: Exception) {
            logger.error("Error fetching user profile", e)
            call.respondTemplate("user/profile", mapOf(
                "user" to session,
                "error" to "Connection failed. Please try again later."
            ))
        }
    }

    suspend fun getUserPayments(call: ApplicationCall) {
        val session = call.sessions.get<Session>()
        if (session == null) {
            call.respondRedirect("/auth/login")
            return
        }

        try {
            // Get device ID for the API request
            val deviceId = DeviceIdManager.getOrCreateDeviceId(call)
            
            val response = client.get("$baseUrl/v3/data/me/payments") {
                headers {
                    append(HttpHeaders.Authorization, "Bearer ${session.token}")
                    append("X-Device-Id", deviceId)
                    append("X-Device-Type", "web")
                    headers?.get("User-Agent")?.let { append("User-Agent", it) }
                }
            }

            when (response.status) {
                HttpStatusCode.OK -> {
                    try {
                        val responseText = response.bodyAsText()
                        val paymentsResponse = Json.decodeFromString<PaymentResponse>(responseText)
                        
                        call.respondTemplate("user/payments", mapOf(
                            "user" to session,
                            "payments" to paymentsResponse.data,
                            "meta" to paymentsResponse.pagination
                        ))
                    } catch (e: Exception) {
                        logger.error("Failed to parse payments response", e)
                        call.respondTemplate("user/payments", mapOf(
                            "user" to session,
                            "error" to "Failed to load payment data"
                        ))
                    }
                }
                HttpStatusCode.Unauthorized -> {
                    // Token expired, redirect to login
                    call.sessions.clear<Session>()
                    call.respondRedirect("/auth/login")
                }
                else -> {
                    logger.error("Failed to get user payments: ${response.status}")
                    call.respondTemplate("user/payments", mapOf(
                        "user" to session,
                        "error" to "Failed to load payment data"
                    ))
                }
            }
        } catch (e: Exception) {
            logger.error("Error fetching user payments", e)
            call.respondTemplate("user/payments", mapOf(
                "user" to session,
                "error" to "Connection failed. Please try again later."
            ))
        }
    }

    suspend fun getUserPurchases(call: ApplicationCall) {
        val session = call.sessions.get<Session>()
        if (session == null) {
            call.respondRedirect("/auth/login")
            return
        }

        try {
            // Get device ID for the API request
            val deviceId = DeviceIdManager.getOrCreateDeviceId(call)
            
            val response = client.get("$baseUrl/v3/data/me/purchases") {
                headers {
                    append(HttpHeaders.Authorization, "Bearer ${session.token}")
                    append("X-Device-Id", deviceId)
                    append("X-Device-Type", "web")
                    headers?.get("User-Agent")?.let { append("User-Agent", it) }
                }
            }

            when (response.status) {
                HttpStatusCode.OK -> {
                    try {
                        val responseText = response.bodyAsText()
                        val purchasesResponse = Json.decodeFromString<PurchaseResponse>(responseText)
                        
                        call.respondTemplate("user/purchases", mapOf(
                            "user" to session,
                            "purchases" to purchasesResponse.data,
                            "meta" to purchasesResponse.pagination
                        ))
                    } catch (e: Exception) {
                        logger.error("Failed to parse purchases response", e)
                        call.respondTemplate("user/purchases", mapOf(
                            "user" to session,
                            "error" to "Failed to load purchase data"
                        ))
                    }
                }
                HttpStatusCode.Unauthorized -> {
                    // Token expired, redirect to login
                    call.sessions.clear<Session>()
                    call.respondRedirect("/auth/login")
                }
                else -> {
                    logger.error("Failed to get user purchases: ${response.status}")
                    call.respondTemplate("user/purchases", mapOf(
                        "user" to session,
                        "error" to "Failed to load purchase data"
                    ))
                }
            }
        } catch (e: Exception) {
            logger.error("Error fetching user purchases", e)
            call.respondTemplate("user/purchases", mapOf(
                "user" to session,
                "error" to "Connection failed. Please try again later."
            ))
        }
    }

    fun Route.registerRoutes() {
        authenticate("auth-session") {
            route("/user") {
                get("/profile") {
                    getUserProfile(call)
                }
                
                get("/payments") {
                    getUserPayments(call)
                }
                
                get("/purchases") {
                    getUserPurchases(call)
                }
                
                get("/settings") {
                    getUserProfile(call) // For now, settings page shows profile
                }
            }
        }
    }
}
