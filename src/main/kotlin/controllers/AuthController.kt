package controllers

import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.sessions.*
import kotlinx.serialization.json.Json
import models.*
import org.slf4j.LoggerFactory
import utils.CastleService
import utils.DeviceIdManager
import utils.respondTemplate
import java.time.Instant
import java.time.temporal.ChronoUnit

class AuthController {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val baseUrl = "https://auth.esdiacapp.com"
    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(<PERSON><PERSON> {
                ignoreUnknownKeys = true
                isLenient = true
            })
        }
        
        // Configure timeouts using engine configuration
        engine {
            requestTimeout = 15000 // 15 seconds
            endpoint {
                connectTimeout = 15000 // 15 seconds
                socketTimeout = 15000 // 15 seconds
            }
        }
    }

    suspend fun handleLogin(call: ApplicationCall) {
        try {
            val formParameters = call.receiveParameters()
            val email = formParameters["email"] ?: ""
            val password = formParameters["password"] ?: ""

            if (email.isBlank() || password.isBlank()) {
                call.respondTemplate("auth/login", mapOf(
                    "error" to "Email and password are required",
                    "email" to email,
                    "castleKey" to CastleService.CASTLE_PUBLIC_KEY
                ))
                return
            }

            // Get device ID consistently using the DeviceIdManager
            val deviceId = formParameters["castle_device_id"] ?: DeviceIdManager.getOrCreateDeviceId(call)
            
            logger.info("Using Castle device ID for login: $deviceId")
            
            try {
                val response = client.post("$baseUrl/v3/data/login") {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    
                    // Use Castle device fingerprinting token for device ID
                    header("X-Device-Id", deviceId)
                    header("X-Device-Type", "web")

                    // Include other useful headers for device identification
                    header("X-Remote-Host", call.request.headers["Host"] ?: "")
                    header("User-Agent", call.request.headers["User-Agent"] ?: "")
                    call.request.headers["X-Forwarded-For"]?.let { header("X-Forwarded-For", it) }
                    
                    setBody(LoginRequest(email, password))
                }

                val responseText = response.bodyAsText()
                logger.info("Login API Response [${response.status}]: $responseText")

                when (response.status) {
                    HttpStatusCode.OK -> {
                        try {
                            val authResponse = Json.decodeFromString<EsdiacAuthResponse>(responseText)
                            // Set session expiry to 30 days from now
                            val expiresAt = Instant.now().plus(30, ChronoUnit.DAYS).epochSecond

                            call.sessions.set(Session(
                                userId = authResponse.id,
                                email = authResponse.email,
                                firstName = authResponse.firstName,
                                lastName = authResponse.lastName,
                                token = authResponse.authorizationToken,
                                expiresAt = expiresAt
                            ))
                            call.respondRedirect("/dashboard")
                        } catch (e: Exception) {
                            logger.error("Failed to parse login response", e)
                            call.respondTemplate("auth/login", mapOf(
                                "error" to "Failed to process login response",
                                "email" to email,
                                "castleKey" to CastleService.CASTLE_PUBLIC_KEY
                            ))
                        }
                    }
                    HttpStatusCode.BadRequest, HttpStatusCode.Unauthorized -> {
                        val error = try {
                            Json.decodeFromString<ErrorMessage>(responseText).message
                        } catch (e: Exception) {
                            "Invalid credentials"
                        }
                        call.respondTemplate("auth/login", mapOf(
                            "error" to error,
                            "email" to email,
                            "castleKey" to CastleService.CASTLE_PUBLIC_KEY
                        ))
                    }
                    else -> {
                        logger.error("Login failed with status: ${response.status}")
                        call.respondTemplate("auth/login", mapOf(
                            "error" to "Login failed. Please try again later.",
                            "email" to email,
                            "castleKey" to CastleService.CASTLE_PUBLIC_KEY
                        ))
                    }
                }
            } catch (e: Exception) {
                logger.error("Login request failed", e)
                call.respondTemplate("auth/login", mapOf(
                    "error" to "Connection failed. Please try again later.",
                    "email" to email,
                    "castleKey" to CastleService.CASTLE_PUBLIC_KEY
                ))
            }
        } catch (e: Exception) {
            logger.error("Login handler error", e)
            call.respondTemplate("auth/login", mapOf(
                "error" to "An unexpected error occurred",
                "castleKey" to CastleService.CASTLE_PUBLIC_KEY
            ))
        }
    }

    suspend fun handleRegistration(call: ApplicationCall) {
        try {
            val formParameters = call.receiveParameters()
            val email = formParameters["email"] ?: ""
            val password = formParameters["password"] ?: ""
            val firstName = formParameters["firstName"] ?: ""
            val lastName = formParameters["lastName"] ?: ""
            val countryId = formParameters["countryId"] ?: ""
            val promoCode = formParameters["promoCode"]
            
            // Get device ID consistently using the DeviceIdManager
            val castleDeviceId = formParameters["castle_device_id"] ?: DeviceIdManager.getBestDeviceId(call)
            
            logger.info("Using Castle device ID for registration: $castleDeviceId")

            // Validate required fields
            val missingFields = mutableListOf<String>()
            if (email.isBlank()) missingFields.add("Email")
            if (password.isBlank()) missingFields.add("Password")
            if (firstName.isBlank()) missingFields.add("First name")
            if (lastName.isBlank()) missingFields.add("Last name")
            if (countryId.isBlank()) missingFields.add("Country")

            if (missingFields.isNotEmpty()) {
                call.respondTemplate("auth/register", mapOf(
                    "error" to "${missingFields.joinToString(", ")} ${if(missingFields.size == 1) "is" else "are"} required",
                    "email" to email,
                    "firstName" to firstName,
                    "lastName" to lastName,
                    "countryId" to countryId
                ))
                return
            }

            try {
                val registerRequest = RegisterRequest(
                    email = email,
                    password = password,
                    firstName = firstName,
                    lastName = lastName,
                    countryId = countryId,
                    promoCode = promoCode
                )

                val response = client.post("$baseUrl/v3/data/register") {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    
                    // Use Castle device fingerprinting token for device ID
                    header("X-Device-Id", castleDeviceId)
                    header("X-Device-Type", "web")
                    
                    // Include other useful headers for device identification
                    header("X-Remote-Host", call.request.headers["Host"] ?: "")
                    header("User-Agent", call.request.headers["User-Agent"] ?: "")
                    call.request.headers["X-Forwarded-For"]?.let { header("X-Forwarded-For", it) }
                    
                    setBody(registerRequest)
                }

                val responseText = response.bodyAsText()
                logger.info("Register API Response [${response.status}]: $responseText")

                when (response.status) {
                    HttpStatusCode.OK -> {
                        try {
                            val authResponse = Json.decodeFromString<AuthResponse>(responseText)
                            // Set session expiry to 30 days from now
                            val expiresAt = Instant.now().plus(30, ChronoUnit.DAYS).epochSecond
                            
                            call.sessions.set(Session(
                                userId = authResponse.user.id,
                                email = authResponse.user.email,
                                firstName = authResponse.user.firstName,
                                lastName = authResponse.user.lastName,
                                token = authResponse.token,
                                expiresAt = expiresAt
                            ))
                            call.respondRedirect("/dashboard")
                        } catch (e: Exception) {
                            logger.error("Failed to parse registration response", e)
                            call.respondTemplate("auth/register", mapOf(
                                "error" to "Registration successful but failed to log in automatically",
                                "email" to email,
                                "firstName" to firstName,
                                "lastName" to lastName,
                                "countryId" to countryId
                            ))
                        }
                    }
                    HttpStatusCode.BadRequest -> {
                        val error = try {
                            Json.decodeFromString<ErrorResponse>(responseText).message
                        } catch (e: Exception) {
                            "Invalid registration data"
                        }
                        call.respondTemplate("auth/register", mapOf(
                            "error" to error,
                            "email" to email,
                            "firstName" to firstName,
                            "lastName" to lastName,
                            "countryId" to countryId
                        ))
                    }
                    HttpStatusCode.Forbidden -> {
                        logger.error("Registration forbidden: $responseText")
                        // For device validation issues, show a more specific error
                        val errorMessage = if (responseText.contains("device invalid")) {
                            "Your device could not be verified. Please ensure cookies are enabled and try again."
                        } else {
                            "Access denied. Please try again later."
                        }
                        call.respondTemplate("auth/register", mapOf(
                            "error" to errorMessage,
                            "email" to email,
                            "firstName" to firstName,
                            "lastName" to lastName,
                            "countryId" to countryId
                        ))
                    }
                    else -> {
                        logger.error("Registration failed: ${response.status}")
                        call.respondTemplate("auth/register", mapOf(
                            "error" to "Registration failed. Please try again later.",
                            "email" to email,
                            "firstName" to firstName,
                            "lastName" to lastName,
                            "countryId" to countryId
                        ))
                    }
                }
            } catch (e: Exception) {
                logger.error("Registration request failed", e)
                call.respondTemplate("auth/register", mapOf(
                    "error" to "Connection failed. Please try again later.",
                    "email" to email,
                    "firstName" to firstName,
                    "lastName" to lastName,
                    "countryId" to countryId
                ))
            }
        } catch (e: Exception) {
            logger.error("Registration handler error", e)
            call.respondTemplate("auth/register", mapOf(
                "error" to "An unexpected error occurred"
            ))
        }
    }

    fun Route.registerRoutes() {
        route("/auth") {
            get("/login") {
                if (call.sessions.get<Session>() != null) {
                    call.respondRedirect("/dashboard")
                    return@get
                }
                call.respondTemplate("auth/login", mapOf(
                    "error" to (call.parameters["error"] ?: ""),
                    "email" to (call.parameters["email"] ?: ""),
                    "castleKey" to CastleService.CASTLE_PUBLIC_KEY
                ))
            }

            post("/login") {
                handleLogin(call)
            }

            get("/logout") {
                call.sessions.clear<Session>()
                call.respondRedirect("/auth/login")
            }

            get("/register") {
                if (call.sessions.get<Session>() != null) {
                    call.respondRedirect("/dashboard")
                    return@get
                }
                call.respondTemplate("auth/register", mapOf(
                    "error" to (call.parameters["error"] ?: ""),
                    "email" to (call.parameters["email"] ?: ""),
                    "firstName" to (call.parameters["firstName"] ?: ""),
                    "lastName" to (call.parameters["lastName"] ?: ""),
                    "countryId" to (call.parameters["countryId"] ?: ""),
                    "castleKey" to CastleService.CASTLE_PUBLIC_KEY
                ))
            }

            post("/register") {
                handleRegistration(call)
            }
        }
    }
}
