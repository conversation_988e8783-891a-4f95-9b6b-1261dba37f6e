package controllers

import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.auth.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.sessions.*
import kotlinx.serialization.json.Json
import models.*
import org.slf4j.LoggerFactory
import utils.respondTemplate

// Remove these extension properties
// private val PurchaseResponse.meta: Any
// private val PaymentResponse.meta: Any

class ProfileController {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                prettyPrint = true
                isLenient = true
            })
        }
        expectSuccess = false
    }

    private val baseUrl = "https://auth.esdiacapp.com"

    fun Route.registerRoutes() {
        authenticate("auth-session") {
            route("/profile") {
                get {
                    try {
                        val session = call.sessions.get<Session>()
                        
                        // Get device ID from cookie or generate new one
                        val deviceIdCookie = call.request.cookies["device_id"]
                        // Use a consistent format for device IDs - prefix with "web-" to indicate web platform
                        val deviceId = deviceIdCookie ?: "web-${java.util.UUID.randomUUID()}"
                        
                        // Set device ID cookie if it was missing
                        if (deviceIdCookie == null) {
                            call.response.cookies.append(
                                name = "device_id",
                                value = deviceId,
                                maxAge = 365 * 24 * 60 * 60, // 1 year in seconds
                                path = "/",
                                httpOnly = true
                            )
                        }
                        
                        val response = client.get("$baseUrl/v3/data/me") {
                            headers {
                                append(HttpHeaders.Authorization, "Bearer ${session?.token}")
                                append("X-Device-Id", deviceId)
                                append("X-Device-Type", "web")
                                append("X-Remote-Host", call.request.headers["Host"] ?: "")
                                append("User-Agent", call.request.headers["User-Agent"] ?: "")
                                call.request.headers["X-Forwarded-For"]?.let { append("X-Forwarded-For", it) }
                            }
                        }

                        when (response.status) {
                            HttpStatusCode.OK -> {
                                val profile = Json.decodeFromString<UserProfile>(response.body<String>())
                                call.respondTemplate("profile/index", mapOf(
                                    "profile" to profile,
                                    "title" to "My Profile"
                                ))
                            }
                            else -> {
                                call.respondRedirect("/auth/login")
                            }
                        }
                    } catch (e: Exception) {
                        logger.error("Error fetching profile", e)
                        call.respondRedirect("/auth/login")
                    }
                }

                get("/payments") {
                    try {
                        val session = call.sessions.get<Session>()
                        
                        // Get device ID from cookie
                        val deviceId = call.request.cookies["device_id"] ?: java.util.UUID.randomUUID().toString().also {
                            call.response.cookies.append(
                                name = "device_id",
                                value = it,
                                maxAge = 365 * 24 * 60 * 60,
                                path = "/",
                                httpOnly = true
                            )
                        }
                        
                        val response = client.get("$baseUrl/v3/data/me/payments") {
                            headers {
                                append(HttpHeaders.Authorization, "Bearer ${session?.token}")
                                append("X-Device-Id", deviceId)
                            }
                        }

                        when (response.status) {
                            HttpStatusCode.OK -> {
                                val payments = response.body<PaymentResponse>()
                                // Get rate limit info from headers instead of meta
                                val rateLimit = RateLimitInfo(
                                    limit = response.headers["X-RateLimit-Limit"]?.toIntOrNull() ?: 0,
                                    remaining = response.headers["X-RateLimit-Remaining"]?.toIntOrNull() ?: 0,
                                    reset = response.headers["X-RateLimit-Reset"] ?: ""
                                )
                                
                                call.respondTemplate("profile/payments", mapOf(
                                    "payments" to payments.data,
                                    "pagination" to payments.pagination,
                                    "rateLimit" to rateLimit,
                                    "title" to "Payment History"
                                ))
                            }
                            else -> {
                                call.respondRedirect("/auth/login")
                            }
                        }
                    } catch (e: Exception) {
                        logger.error("Error fetching payments", e)
                        call.respondRedirect("/auth/login")
                    }
                }

                get("/purchases") {
                    try {
                        val session = call.sessions.get<Session>()
                        
                        // Get device ID from cookie
                        val deviceId = call.request.cookies["device_id"] ?: java.util.UUID.randomUUID().toString().also {
                            call.response.cookies.append(
                                name = "device_id",
                                value = it,
                                maxAge = 365 * 24 * 60 * 60,
                                path = "/",
                                httpOnly = true
                            )
                        }
                        
                        val response = client.get("$baseUrl/v3/data/me/purchases") {
                            headers {
                                append(HttpHeaders.Authorization, "Bearer ${session?.token}")
                                append("X-Device-Id", deviceId)
                            }
                        }

                        when (response.status) {
                            HttpStatusCode.OK -> {
                                val purchases = response.body<PurchaseResponse>()
                                // Get rate limit info from headers instead of meta
                                val rateLimit = RateLimitInfo(
                                    limit = response.headers["X-RateLimit-Limit"]?.toIntOrNull() ?: 0,
                                    remaining = response.headers["X-RateLimit-Remaining"]?.toIntOrNull() ?: 0,
                                    reset = response.headers["X-RateLimit-Reset"] ?: ""
                                )
                                
                                call.respondTemplate("profile/purchases", mapOf(
                                    "purchases" to purchases.data,
                                    "pagination" to purchases.pagination,
                                    "rateLimit" to rateLimit,
                                    "title" to "Purchase History"
                                ))
                            }
                            else -> {
                                call.respondRedirect("/auth/login")
                            }
                        }
                    } catch (e: Exception) {
                        logger.error("Error fetching purchases", e)
                        call.respondRedirect("/auth/login")
                    }
                }

                post("/logout") {
                    try {
                        val session = call.sessions.get<Session>()
                        
                        // Get device ID from cookie
                        val deviceId = call.request.cookies["device_id"] ?: java.util.UUID.randomUUID().toString()
                        
                        client.post("$baseUrl/v3/data/me/logout") {
                            headers {
                                append(HttpHeaders.Authorization, "Bearer ${session?.token}")
                                append("X-Device-Id", deviceId)
                            }
                        }
                    } catch (e: Exception) {
                        logger.error("Error during logout", e)
                    } finally {
                        call.sessions.clear<Session>()
                        call.respondRedirect("/auth/login")
                    }
                }

                post("/settings/profile") {
                    val session = call.sessions.get<Session>()
                    val params = call.receiveParameters()
                    val firstName = params["firstName"] ?: ""
                    val lastName = params["lastName"] ?: ""
                    
                    // Get device ID from cookie
                    val deviceId = call.request.cookies["device_id"] ?: java.util.UUID.randomUUID().toString().also {
                        call.response.cookies.append(
                            name = "device_id",
                            value = it,
                            maxAge = 365 * 24 * 60 * 60,
                            path = "/",
                            httpOnly = true
                        )
                    }
                    
                    try {
                        val response = client.put("$baseUrl/v3/data/me/profile") {
                            headers {
                                append(HttpHeaders.Authorization, "Bearer ${session?.token}")
                                append("X-Device-Id", deviceId)
                            }
                            setBody(mapOf("firstName" to firstName, "lastName" to lastName))
                        }
                        if (response.status == HttpStatusCode.OK) {
                            call.respondTemplate("profile/settings", mapOf("success" to "Profile updated successfully!", "user" to mapOf("firstName" to firstName, "lastName" to lastName, "email" to session?.email)))
                        } else {
                            call.respondTemplate("profile/settings", mapOf("error" to "Failed to update profile.", "user" to mapOf("firstName" to firstName, "lastName" to lastName, "email" to session?.email)))
                        }
                    } catch (e: Exception) {
                        logger.error("Error updating profile", e)
                        call.respondTemplate("profile/settings", mapOf("error" to "Error updating profile.", "user" to mapOf("firstName" to firstName, "lastName" to lastName, "email" to session?.email)))
                    }
                }

                post("/settings/password") {
                    val session = call.sessions.get<Session>()
                    val params = call.receiveParameters()
                    val currentPassword = params["currentPassword"] ?: ""
                    val newPassword = params["newPassword"] ?: ""
                    val confirmPassword = params["confirmPassword"] ?: ""
                    if (newPassword != confirmPassword) {
                        call.respondTemplate("profile/settings", mapOf("error" to "Passwords do not match."))
                        return@post
                    }
                    
                    // Get device ID from cookie
                    val deviceId = call.request.cookies["device_id"] ?: java.util.UUID.randomUUID().toString().also {
                        call.response.cookies.append(
                            name = "device_id",
                            value = it,
                            maxAge = 365 * 24 * 60 * 60,
                            path = "/",
                            httpOnly = true
                        )
                    }
                    
                    try {
                        val response = client.put("$baseUrl/v3/data/me/password") {
                            headers {
                                append(HttpHeaders.Authorization, "Bearer ${session?.token}")
                                append("X-Device-Id", deviceId)
                            }
                            setBody(mapOf("currentPassword" to currentPassword, "newPassword" to newPassword))
                        }
                        if (response.status == HttpStatusCode.OK) {
                            call.respondTemplate("profile/settings", mapOf("success" to "Password changed successfully!"))
                        } else {
                            call.respondTemplate("profile/settings", mapOf("error" to "Failed to change password."))
                        }
                    } catch (e: Exception) {
                        logger.error("Error changing password", e)
                        call.respondTemplate("profile/settings", mapOf("error" to "Error changing password."))
                    }
                }

                post("/settings/address") {
                    val session = call.sessions.get<Session>()
                    val params = call.receiveParameters()
                    val address = mapOf(
                        "iso2" to (params["iso2"] ?: ""),
                        "state" to (params["state"] ?: ""),
                        "city" to (params["city"] ?: ""),
                        "zipCode" to (params["zipCode"] ?: ""),
                        "streetAddress" to (params["streetAddress"] ?: ""),
                        "extendedAddress" to (params["extendedAddress"] ?: "")
                    )
                    
                    // Get device ID from cookie
                    val deviceId = call.request.cookies["device_id"] ?: java.util.UUID.randomUUID().toString().also {
                        call.response.cookies.append(
                            name = "device_id",
                            value = it,
                            maxAge = 365 * 24 * 60 * 60,
                            path = "/",
                            httpOnly = true
                        )
                    }
                    
                    try {
                        val response = client.put("$baseUrl/v3/data/me/address") {
                            headers {
                                append(HttpHeaders.Authorization, "Bearer ${session?.token}")
                                append("X-Device-Id", deviceId)
                            }
                            setBody(address)
                        }
                        if (response.status == HttpStatusCode.OK) {
                            call.respondTemplate("profile/settings", mapOf("success" to "Address updated successfully!", "user" to mapOf("address" to address)))
                        } else {
                            call.respondTemplate("profile/settings", mapOf("error" to "Failed to update address.", "user" to mapOf("address" to address)))
                        }
                    } catch (e: Exception) {
                        logger.error("Error updating address", e)
                        call.respondTemplate("profile/settings", mapOf("error" to "Error updating address.", "user" to mapOf("address" to address)))
                    }
                }
            }
        }
    }
}
