package services

import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeout
import kotlinx.serialization.json.Json
import models.*
import org.slf4j.LoggerFactory
import utils.CacheManager
import java.util.*

class GiftCardService {
    private val logger = LoggerFactory.getLogger(GiftCardService::class.java)
    private val baseUrl = "https://api.esdiacapp.com"
    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
            })
        }
        
        // Configure timeouts using engine configuration
        engine {
            requestTimeout = 15000 // 15 seconds
            endpoint {
                connectTimeout = 15000 // 15 seconds
                socketTimeout = 15000 // 15 seconds
            }
        }
    }

    private var headers: Headers? = null
    private var token: String? = null
    private var deviceId: String? = null

    fun init(requestHeaders: Headers, authToken: String?, deviceIdentifier: String) {
        headers = requestHeaders
        token = authToken
        deviceId = deviceIdentifier
    }

    suspend fun getAllCountries(): GiftCardResponse? {
        return try {
            val response = client.get("$baseUrl/v3/data/gift-card/offers/countries") {
                headers {
                    if (!token.isNullOrBlank()) {
                        append(HttpHeaders.Authorization, "Bearer $token")
                    }
                    // Always include the device ID header as required by the API
                    append("X-Device-Id", deviceId ?: "")
                    append("X-Device-Type", "web")
                    headers?.get("User-Agent")?.let { append("User-Agent", it) }
                }
            }

            when (response.status) {
                HttpStatusCode.OK -> {
                    val json = Json { ignoreUnknownKeys = true }
                    val responseText = response.bodyAsText()
                    logger.debug("Countries response: $responseText")
                    
                    try {
                        val giftOffersCountries = json.decodeFromString<GiftOffersCountries>(responseText)
                        
                        // Convert to our internal GiftCardResponse format
                        val countries = giftOffersCountries.data.map { countryData ->
                            GiftCardCountry(
                                id = countryData.country,
                                name = countryData.country,
                                imageUrl = "https://flagcdn.com/w160/${countryData.country.lowercase()}.png",
                                offerCount = countryData.categories.sumOf { it.brands.size }
                            )
                        }
                        return GiftCardResponse(countries = countries)
                    } catch (e: Exception) {
                        logger.error("Failed to parse countries response", e)
                        null
                    }
                }
                else -> {
                    logger.error("Failed to get countries: ${response.status}")
                    null
                }
            }
        } catch (e: Exception) {
            logger.error("Error fetching countries", e)
            null
        }
    }

    suspend fun getGiftCardsByCountry(countryId: String): GiftCardResponse? {
        return try {
            val response = client.get("$baseUrl/v3/data/gift-card/offers/countries/$countryId") {
                headers {
                    if (!token.isNullOrBlank()) {
                        append(HttpHeaders.Authorization, "Bearer $token")
                    }
                    // Always include the device ID header as required by the API
                    append("X-Device-Id", deviceId ?: "")
                    append("X-Device-Type", "web")
                    headers?.get("User-Agent")?.let { append("User-Agent", it) }
                }
            }

            when (response.status) {
                HttpStatusCode.OK -> {
                    val json = Json { ignoreUnknownKeys = true }
                    val responseText = response.bodyAsText()
                    logger.debug("Categories response: $responseText")
                    
                    try {
                        val giftOffersCategories = json.decodeFromString<GiftOffersCategories>(responseText)
                        
                        // Convert to our internal GiftCardResponse format
                        val categories = giftOffersCategories.data.map { brand ->
                            GiftCardCategory(
                                id = brand.category,
                                name = brand.category,
                                imageUrl = "", // No image URL in the API response
                                planCount = brand.brands.size
                            )
                        }
                        return GiftCardResponse(categories = categories)
                    } catch (e: Exception) {
                        logger.error("Failed to parse categories response", e)
                        null
                    }
                }
                else -> {
                    logger.error("Failed to get categories for country $countryId: ${response.status}")
                    null
                }
            }
        } catch (e: Exception) {
            logger.error("Error fetching categories for country $countryId", e)
            null
        }
    }

    suspend fun getGiftCardsByCategory(countryId: String, categoryId: String): GiftCardResponse? {
        return try {
            val response = client.get("$baseUrl/v1/countries/$countryId/categories/$categoryId/brands") {
                headers {
                    append(HttpHeaders.Authorization, "Bearer $token")
                    append("X-Device-Id", deviceId ?: "")
                    append("X-Device-Type", "web")
                    headers?.get("User-Agent")?.let { append("User-Agent", it) }
                }
            }

            when (response.status) {
                HttpStatusCode.OK -> {
                    val json = Json { ignoreUnknownKeys = true }
                    val brandResponse = json.decodeFromString<BrandResponse>(response.bodyAsText())
                    
                    // Convert BrandResponse to GiftCardResponse
                    val brands = brandResponse.brands.map { brand ->
                        GiftCardBrand(
                            id = brand.id,
                            name = brand.name,
                            imageUrl = brand.imageUrl,
                            offerCount = 0 // Default value
                        )
                    }
                    return GiftCardResponse(brands = brands)
                }
                else -> {
                    logger.error("Failed to get brands for country $countryId and category $categoryId: ${response.status}")
                    null
                }
            }
        } catch (e: Exception) {
            logger.error("Error fetching brands for country $countryId and category $categoryId", e)
            null
        }
    }

    suspend fun getGiftCardsByBrand(countryId: String, categoryId: String, brandId: String): GiftCardResponse? {
        return try {
            val response = client.get("$baseUrl/v1/countries/$countryId/categories/$categoryId/brands/$brandId/offers") {
                headers {
                    append(HttpHeaders.Authorization, "Bearer $token")
                    append("X-Device-Id", deviceId ?: "")
                    append("X-Device-Type", "web")
                    headers?.get("User-Agent")?.let { append("User-Agent", it) }
                }
            }

            when (response.status) {
                HttpStatusCode.OK -> {
                    val json = Json { ignoreUnknownKeys = true }
                    val offerResponse = json.decodeFromString<OfferResponse>(response.bodyAsText())
                    
                    // Convert OfferResponse to GiftCardResponse
                    val offers = offerResponse.offers.map { offer ->
                        GiftCardOffer(
                            id = offer.id,
                            name = offer.name,
                            description = offer.description,
                            shortNotes = offer.shortNotes,
                            imageUrl = offer.imageUrl,
                            price = offer.price,
                            currency = offer.currency,
                            countryId = countryId,
                            categoryId = categoryId,
                            brandId = brandId,
                            enabled = offer.enabled,
                            offerType = offer.offerType,
                            minAmount = offer.minAmount,
                            maxAmount = offer.maxAmount
                        )
                    }
                    GiftCardResponse(offers = offers)
                }
                else -> {
                    logger.error("Failed to get offers for country $countryId, category $categoryId, and brand $brandId: ${response.status}")
                    null
                }
            }
        } catch (e: Exception) {
            logger.error("Error fetching offers for country $countryId, category $categoryId, and brand $brandId", e)
            // Fallback with default offers
            GiftCardResponse(offers = listOf(
                GiftCardOffer(
                    id = "1",
                    name = "Gift Card $25",
                    description = "Gift Card",
                    imageUrl = "",
                    price = 25.0,
                    currency = "USD",
                    countryId = countryId,
                    categoryId = categoryId,
                    brandId = brandId
                ),
                GiftCardOffer(
                    id = "2",
                    name = "Gift Card $50",
                    description = "Gift Card",
                    imageUrl = "",
                    price = 50.0,
                    currency = "USD",
                    countryId = countryId,
                    categoryId = categoryId,
                    brandId = brandId
                )
            ))
        }
    }

    suspend fun getGiftCardDetails(offerId: String): GiftCardOffer? {
        try {
            val response = client.get("$baseUrl/v1/offers/$offerId") {
                headers {
                    append(HttpHeaders.Authorization, "Bearer $token")
                    append("X-Device-Id", deviceId ?: "")
                    append("X-Device-Type", "web")
                    headers?.get("User-Agent")?.let { append("User-Agent", it) }
                }
            }

            when (response.status) {
                HttpStatusCode.OK -> {
                    val json = Json { ignoreUnknownKeys = true }
                    return json.decodeFromString<GiftCardOffer>(response.bodyAsText())
                }
                else -> {
                    logger.error("Failed to get offer details for $offerId: ${response.status}")
                    return null
                }
            }
        } catch (e: Exception) {
            logger.error("Error fetching offer details for $offerId", e)
            return null
        }
    }

    suspend fun purchaseGiftCard(offerId: String, amount: Int): GiftCardPurchaseResult? {
        try {
            // Simulate a purchase API call
            delay(1000)
            
            // For demo purposes, randomly succeed or fail
            val success = (0..10).random() > 2
            
            return if (success) {
                GiftCardPurchaseResult(
                    success = true,
                    purchaseId = "purchase_${UUID.randomUUID()}",
                    amount = amount.toDouble(),
                    currency = "USD"
                )
            } else {
                GiftCardPurchaseResult(
                    success = false,
                    message = "Purchase failed. Please try again."
                )
            }
        } catch (e: Exception) {
            logger.error("Error purchasing gift card", e)
            return GiftCardPurchaseResult(
                success = false,
                message = "An error occurred: ${e.message}"
            )
        }
    }

    suspend fun getAllGiftCards(): GiftCardResponse? {
        return CacheManager.getOrPut("all_giftcards", ttl = 5 * 60 * 1000) { // 5 minutes cache
            try {
                val response = withTimeout<GiftCardResponse?>(3000) { // 3 second timeout
                    getAllCountries() // Use existing method instead of authService
                }

                val offers = response?.countries?.take(6)?.mapIndexed { index, country ->
                    GiftCardOffer(
                        id = "offer_$index",
                        name = "Gift Card Offer ${index + 1}",
                        description = "Gift Card Description",
                        imageUrl = "",
                        minAmount = 10.0,
                        maxAmount = 100.0,
                        price = (10 + index * 5).toDouble(),
                        currency = "USD",
                        countryId = country.id,
                        categoryId = "retail",
                        brandId = "brand_$index"
                    )
                } ?: emptyList()

                if (offers.isNotEmpty()) {
                    GiftCardResponse(offers = offers)
                } else {
                    // Fallback with default offers
                    GiftCardResponse(offers = listOf(
                        GiftCardOffer(id = "1", name = "Amazon Gift Card", description = "Amazon.com Gift Card", imageUrl = "", price = 25.0, currency = "USD"),
                        GiftCardOffer(id = "2", name = "Netflix Gift Card", description = "Netflix streaming", imageUrl = "", price = 15.0, currency = "USD"),
                        GiftCardOffer(id = "3", name = "Starbucks Gift Card", description = "Coffee and treats", imageUrl = "", price = 10.0, currency = "USD")
                    ))
                }
            } catch (e: Exception) {
                e.printStackTrace()
                // Fallback with default offers
                GiftCardResponse(offers = listOf(
                    GiftCardOffer(id = "1", name = "Amazon Gift Card", description = "Amazon.com Gift Card", imageUrl = "", price = 25.0, currency = "USD"),
                    GiftCardOffer(id = "2", name = "Netflix Gift Card", description = "Netflix streaming", imageUrl = "", price = 15.0, currency = "USD"),
                    GiftCardOffer(id = "3", name = "Starbucks Gift Card", description = "Coffee and treats", imageUrl = "", price = 10.0, currency = "USD")
                ))
            }
        }
    }
}

@kotlinx.serialization.Serializable
data class CategoryResponse(
    val categories: List<Category> = emptyList()
)

@kotlinx.serialization.Serializable
data class Category(
    val id: String,
    val name: String,
    val imageUrl: String
)

@kotlinx.serialization.Serializable
data class BrandResponse(
    val brands: List<Brand> = emptyList()
)

@kotlinx.serialization.Serializable
data class Brand(
    val id: String,
    val name: String,
    val imageUrl: String
)

@kotlinx.serialization.Serializable
data class OfferResponse(
    val offers: List<Offer> = emptyList()
)

@kotlinx.serialization.Serializable
data class Offer(
    val id: String,
    val name: String,
    val description: String,
    val shortNotes: String? = null,
    val imageUrl: String,
    val price: Double,
    val currency: String,
    val enabled: Boolean = true,
    val offerType: String = "gift-card",
    val minAmount: Double = 0.0,
    val maxAmount: Double = 0.0
)