package models

import io.ktor.server.auth.*
import kotlinx.serialization.Serializable

// ESDIAC API Request Models
@Serializable
data class LoginRequest(
    val email: String,
    val password: String
)

@Serializable
data class RegisterRequest(
    val email: String,
    val password: String,
    val firstName: String,
    val lastName: String,
    val countryId: String,
    val promoCode: String? = null
)

@Serializable
data class AuthResponse(
    val token: String,
    val user: User,
    val expiresAt: String? = null
)

@Serializable
data class ErrorResponse(
    val message: String
)

@Serializable
data class User(
    val id: String,
    val email: String,
    val firstName: String,
    val lastName: String,
    val countryId: String,
    val createdAt: String,
    val updatedAt: String
)

// Session class that implements Principal for authentication
@Serializable
data class Session(
    val userId: String,
    val email: String,
    val firstName: String,
    val lastName: String,
    val token: String,
    val expiresAt: Long
) : Principal
