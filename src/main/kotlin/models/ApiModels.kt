package models

import kotlinx.serialization.Serializable

// Meta and ErrorMessage moved to avoid duplication
// Meta is defined in ApiResponse.kt
// ErrorMessage is defined in DataCustomer.kt

@Serializable
data class GiftOffersCountries(
    val meta: Meta,
    val data: List<GiftOfferCategory>
)

@Serializable
data class GiftOfferCategory(
    val category: String,
    val brands: List<GiftOfferBrand>
)

@Serializable
data class GiftOfferBrand(
    val brand: String,
    val offers: List<GiftOffer>
)

@Serializable
data class GiftOffersCategories(
    val meta: Meta,
    val data: List<GiftOfferBrand>
)

@Serializable
data class GiftOffer(
    val enabled: Boolean,
    val offerId: String,
    val offerType: String,
    val categoryId: String? = null,
    val categoryName: String? = null,
    val brandId: String? = null,
    val brandName: String,
    val countryId: String,
    val countryName: String,
    val notes: String,
    val shortNotes: String,
    val price: OfferPrice
)

@Serializable
data class OfferPrice(
    val currency: String,
    val fixed: String
)

@Serializable
data class DataCustomerWithCredential(
    val id: String,
    val email: String,
    val firstName: String,
    val lastName: String,
    val referralCode: String,
    val authorizationToken: String,
    val authorizationSignature: String,
    val promoCode: String? = null
)