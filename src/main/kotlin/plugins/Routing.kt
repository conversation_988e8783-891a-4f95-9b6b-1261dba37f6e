package plugins

import controllers.ApiController
import controllers.AuthController
import controllers.DashboardController
import controllers.GiftCardController
import controllers.ProfileController
import controllers.UserController
import io.ktor.server.application.*
import io.ktor.server.http.content.*
import io.ktor.server.routing.*
import utils.respondTemplate

fun Application.configureRouting() {
    routing {
        // Static resources
        static("/static") {
            resources("static")
        }
        
        ApiController().apply { registerRoutes() }
        AuthController().apply { registerRoutes() }
        DashboardController().apply { registerRoutes() }
        GiftCardController().apply { registerRoutes() }
        ProfileController().apply { registerRoutes() }
        UserController().apply { registerRoutes() }

        // Add a test route
        get("/test") {
            call.respondTemplate("test")
        }
    }
}