ktor {
    deployment {
        port = 8081
        port = ${?PORT}
        connectionTimeoutSeconds = 30
        shutdownGracePeriodSeconds = 30
        shutdownTimeoutSeconds = 30
    }
    application {
        modules = [ ApplicationKt.module ]
    }
    development = false
}

app {
    internal-services {
        auth_service = "https://api.esdiacapp.com"
        connection_timeout = 30
        read_timeout = 30
        retry_attempts = 3
        retry_delay = 1000
    }
}

logging {
    loggers = ["FILE", "STDOUT"]
    level = "DEBUG"

    appenders {
        file {
            type = "FILE"
            filename = "api_logs.txt"
            pattern = "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
            append = true
            rollingPolicy {
                type = "SizeBasedTriggeringPolicy"
                maxFileSize = "10MB"
            }
        }
        console {
            type = "CONSOLE"
            pattern = "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
        }
    }
}
